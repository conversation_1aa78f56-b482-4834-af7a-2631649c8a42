import { BlogPostGrid } from '@/components/blog-post-card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SeoHead from '@/components/seo-head';
import PublicLayout from '@/layouts/public-layout';
import { type Category } from '@/types';
import { Link, router } from '@inertiajs/react';
import { Search, ArrowRight, X, ChevronLeft, ChevronRight } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';

interface BlogProps {
    blogPosts: {
        data: Array<{
            id: number;
            title: string;
            slug: string;
            excerpt: string;
            content: string;
            published_at: string;
            views_count: number;
            category?: Category;
            author?: {
                name: string;
                avatar?: string;
            };
        }>;
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    categories: Category[];
    filters: {
        search?: string;
        category_id?: string;
        sort_by: string;
        sort_direction: string;
    };
    meta: {
        title: string;
        description: string;
        keywords?: string;
        canonical: string;
        og_title?: string;
        og_description?: string;
        og_type?: string;
        og_url?: string;
        og_site_name?: string;
        og_image?: string;
        twitter_card?: string;
        twitter_title?: string;
        twitter_description?: string;
        twitter_image?: string;
    };
}

export default function Blog({ blogPosts, categories, filters, meta }: BlogProps) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [selectedCategory, setSelectedCategory] = useState(filters.category_id || '');
    const [sortBy, setSortBy] = useState(filters.sort_by || 'published_at');
    const [sortDirection, setSortDirection] = useState(filters.sort_direction || 'desc');

    // Get posts data (handle both paginated and non-paginated responses)
    const posts = blogPosts.data || blogPosts || [];
    const pagination = blogPosts.links ? {
        current_page: blogPosts.current_page,
        last_page: blogPosts.last_page,
        prev_page_url: blogPosts.prev_page_url,
        next_page_url: blogPosts.next_page_url,
        links: blogPosts.links
    } : null;

    // Handle search and filter changes
    const handleSearch = useCallback(() => {
        const params = new URLSearchParams(window.location.search);
        if (searchTerm) params.set('search', searchTerm);
        else params.delete('search');

        if (selectedCategory) params.set('category_id', selectedCategory);
        else params.delete('category_id');

        if (sortBy !== 'published_at') params.set('sort_by', sortBy);
        else params.delete('sort_by');

        if (sortDirection !== 'desc') params.set('sort_direction', sortDirection);
        else params.delete('sort_direction');

        // Reset to first page when filtering
        params.delete('page');

        router.get('/blog', Object.fromEntries(params), {
            preserveState: true,
            preserveScroll: true,
        });
    }, [searchTerm, selectedCategory, sortBy, sortDirection]);

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedCategory('');
        setSortBy('published_at');
        setSortDirection('desc');
        router.get('/blog', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // Auto-search on input change with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== (filters.search || '')) {
                handleSearch();
            }
        }, 500);
        return () => clearTimeout(timeoutId);
    }, [searchTerm, filters.search, handleSearch]);

    const hasActiveFilters = searchTerm || selectedCategory || sortBy !== 'published_at' || sortDirection !== 'desc';

    return (
        <PublicLayout>
            <SeoHead meta={meta} />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-br from-background via-background to-muted/20">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 px-4 py-2">
                            Blog & Resources
                        </Badge>
                        <h1 className="text-4xl font-bold mb-6 sm:text-5xl">
                            Digital Marketing Insights
                        </h1>
                        <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                            Stay updated with the latest strategies, tips, and insights in Meta advertising, 
                            web analytics, and digital marketing optimization.
                        </p>
                    </div>
                </div>
            </section>

            {/* Search and Filter */}
            <section className="py-8 bg-muted/30">
                <div className="container mx-auto px-4">
                    <div className="max-w-4xl mx-auto">
                        <div className="flex flex-col md:flex-row gap-4">
                            {/* Search */}
                            <div className="flex-1 relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    type="text"
                                    placeholder="Search articles..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                            
                            {/* Category Filter */}
                            <div className="md:w-48">
                                <Select value={selectedCategory} onValueChange={(value) => {
                                    setSelectedCategory(value);
                                    setTimeout(handleSearch, 100);
                                }}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Categories" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">All Categories</SelectItem>
                                        {categories.map((category) => (
                                            <SelectItem key={category.id} value={category.id.toString()}>
                                                {category.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Sort Options */}
                            <div className="md:w-48">
                                <Select value={`${sortBy}-${sortDirection}`} onValueChange={(value) => {
                                    const [field, direction] = value.split('-');
                                    setSortBy(field);
                                    setSortDirection(direction);
                                    setTimeout(handleSearch, 100);
                                }}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Sort by" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="published_at-desc">Latest First</SelectItem>
                                        <SelectItem value="published_at-asc">Oldest First</SelectItem>
                                        <SelectItem value="title-asc">Title (A-Z)</SelectItem>
                                        <SelectItem value="title-desc">Title (Z-A)</SelectItem>
                                        <SelectItem value="views_count-desc">Most Popular</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Clear Filters */}
                            {hasActiveFilters && (
                                <Button variant="outline" onClick={clearFilters} className="whitespace-nowrap">
                                    <X className="h-4 w-4 mr-2" />
                                    Clear
                                </Button>
                            )}
                        </div>

                        {/* Active Filters Display */}
                        {hasActiveFilters && (
                            <div className="mt-4 flex flex-wrap gap-2">
                                {searchTerm && (
                                    <Badge variant="secondary">
                                        Search: "{searchTerm}"
                                    </Badge>
                                )}
                                {selectedCategory && (
                                    <Badge variant="secondary">
                                        Category: {categories.find(c => c.id.toString() === selectedCategory)?.name}
                                    </Badge>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {/* Blog Posts */}
            <section className="py-16">
                <div className="container mx-auto px-4">
                    <div className="max-w-6xl mx-auto">
                        {posts.length > 0 ? (
                            <>
                                <BlogPostGrid posts={posts} />

                                {/* Pagination */}
                                {pagination && pagination.last_page > 1 && (
                                    <div className="mt-12">
                                        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                                            {/* Results Info */}
                                            <div className="text-sm text-muted-foreground">
                                                Showing page {pagination.current_page} of {pagination.last_page}
                                            </div>

                                            {/* Pagination Controls */}
                                            <div className="flex items-center space-x-2">
                                                {/* Previous Button */}
                                                {pagination.prev_page_url ? (
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => {
                                                            const params = new URLSearchParams(window.location.search);
                                                            params.set('page', (pagination.current_page - 1).toString());
                                                            router.get('/blog', Object.fromEntries(params), {
                                                                preserveState: true,
                                                                preserveScroll: false,
                                                            });
                                                        }}
                                                    >
                                                        <ChevronLeft className="h-4 w-4 mr-1" />
                                                        Previous
                                                    </Button>
                                                ) : (
                                                    <Button variant="outline" disabled>
                                                        <ChevronLeft className="h-4 w-4 mr-1" />
                                                        Previous
                                                    </Button>
                                                )}

                                                {/* Page Numbers */}
                                                <div className="flex items-center space-x-1">
                                                    {Array.from({ length: Math.min(5, pagination.last_page) }, (_, i) => {
                                                        let pageNum;
                                                        if (pagination.last_page <= 5) {
                                                            pageNum = i + 1;
                                                        } else if (pagination.current_page <= 3) {
                                                            pageNum = i + 1;
                                                        } else if (pagination.current_page >= pagination.last_page - 2) {
                                                            pageNum = pagination.last_page - 4 + i;
                                                        } else {
                                                            pageNum = pagination.current_page - 2 + i;
                                                        }

                                                        return (
                                                            <Button
                                                                key={pageNum}
                                                                variant={pageNum === pagination.current_page ? "default" : "outline"}
                                                                size="sm"
                                                                onClick={() => {
                                                                    const params = new URLSearchParams(window.location.search);
                                                                    params.set('page', pageNum.toString());
                                                                    router.get('/blog', Object.fromEntries(params), {
                                                                        preserveState: true,
                                                                        preserveScroll: false,
                                                                    });
                                                                }}
                                                                className="w-10 h-10"
                                                            >
                                                                {pageNum}
                                                            </Button>
                                                        );
                                                    })}
                                                </div>

                                                {/* Next Button */}
                                                {pagination.next_page_url ? (
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => {
                                                            const params = new URLSearchParams(window.location.search);
                                                            params.set('page', (pagination.current_page + 1).toString());
                                                            router.get('/blog', Object.fromEntries(params), {
                                                                preserveState: true,
                                                                preserveScroll: false,
                                                            });
                                                        }}
                                                    >
                                                        Next
                                                        <ChevronRight className="h-4 w-4 ml-1" />
                                                    </Button>
                                                ) : (
                                                    <Button variant="outline" disabled>
                                                        Next
                                                        <ChevronRight className="h-4 w-4 ml-1" />
                                                    </Button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </>
                        ) : hasActiveFilters ? (
                            <div className="text-center py-12">
                                <h3 className="text-2xl font-semibold mb-4">No Articles Found</h3>
                                <p className="text-lg text-muted-foreground mb-8">
                                    Try adjusting your search terms or category filter.
                                </p>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchTerm('');
                                        setSelectedCategory('');
                                    }}
                                >
                                    Clear Filters
                                </Button>
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <h3 className="text-2xl font-semibold mb-4">Coming Soon</h3>
                                <p className="text-lg text-muted-foreground mb-8">
                                    We're working on creating valuable content for you.
                                    Check back soon for the latest insights and strategies.
                                </p>
                                <Button asChild>
                                    <Link href="/contact">
                                        Get Notified When We Publish
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            </section>

            {/* Newsletter CTA */}
            <section className="py-16 bg-primary text-primary-foreground">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-3xl font-bold mb-4">Stay Updated</h2>
                    <p className="text-lg mb-8 opacity-90 max-w-2xl mx-auto">
                        Subscribe to our newsletter and get the latest digital marketing insights delivered to your inbox
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                        <Input 
                            type="email" 
                            placeholder="Enter your email" 
                            className="bg-background text-foreground"
                        />
                        <Button variant="secondary">
                            Subscribe
                        </Button>
                    </div>
                </div>
            </section>
        </PublicLayout>
    );
}
